// 测试根据用户名计算菜品条数的功能

// 模拟原始数据结构 - 模拟杨志国有多个订单的情况
const mockApiResponse = {
  code: 0,
  data: {
    results: [
      {
        "area_name": "A00空分现场",
        "l1_addr": "空分现场",
        "l2_addr": "A00空分现场",
        "user_name": "杨志国",
        "phone": "18531508673",
        "remark": "",
        "orders_count": 1,
        "foods_st": [
          {
            "food_name": "海土豆",
            "food_count": 1
          },
          {
            "food_name": "米饭",
            "food_count": 1
          },
          {
            "food_name": "回锅肉",
            "food_count": 1
          }
        ]
      },
      {
        "area_name": "A00空分现场",
        "l1_addr": "空分现场",
        "l2_addr": "A00空分现场",
        "user_name": "杨志国",
        "phone": "18531508673",
        "remark": "",
        "orders_count": 1,
        "foods_st": [
          {
            "food_name": "白菜炒木耳",
            "food_count": 1
          },
          {
            "food_name": "米饭",
            "food_count": 1
          },
          {
            "food_name": "奥尔良炸鸡腿",
            "food_count": 1
          },
          {
            "food_name": "小炒菜花",
            "food_count": 1
          }
        ]
      },
      {
        "area_name": "A00空分现场",
        "l1_addr": "空分现场",
        "l2_addr": "A00空分现场",
        "user_name": "其他用户",
        "phone": "13000000000",
        "remark": "",
        "orders_count": 1,
        "foods_st": [
          {
            "food_name": "测试菜品",
            "food_count": 1
          }
        ]
      }
    ],
    other_info: [
      { order_payment_id: 'order_001' },
      { order_payment_id: 'order_002' },
      { order_payment_id: 'order_003' }
    ]
  }
}

// 处理数据的函数（修改后的逻辑）
function processTableData(res) {
  const tableData = []

  // 先构建所有表格数据
  res.data.results.map((item, index) => {
    item.foods_st.map(food => {
      tableData.push({
        id: res.data.other_info[index].order_payment_id,
        ...item,
        ...food,
        index
      })
    })
  })

  // 然后按用户名分组计算菜品总数
  const userFoodCounts = {}
  tableData.forEach((row) => {
    if (!userFoodCounts[row.user_name]) {
      userFoodCounts[row.user_name] = 0
    }
    userFoodCounts[row.user_name] += 1 // 每一行代表一种菜品
  })

  console.log('用户菜品统计:', userFoodCounts)

  // 最后为每行数据添加小计
  tableData.forEach((row) => {
    row.subtotal = userFoodCounts[row.user_name]
  })

  return tableData
}

// 演示处理结果
const processedData = processTableData(mockApiResponse)

console.log('处理后的表格数据：')
processedData.forEach((row, index) => {
  console.log(`第${index + 1}行 - 用户: ${row.user_name}, 菜品: ${row.food_name}, 数量: ${row.food_count}, 小计: ${row.subtotal}`)
})

console.log('\n小计计算结果验证：')
console.log('杨志国的菜品条数小计应该是 7 (两个订单共7种菜品):', processedData.find(row => row.user_name === '杨志国').subtotal)
console.log('其他用户的菜品条数小计应该是 1 (1种菜品):', processedData.find(row => row.user_name === '其他用户').subtotal)

console.log('\n详细说明：')
console.log('- 杨志国第一个订单有3种菜品：海土豆、米饭、回锅肉')
console.log('- 杨志国第二个订单有4种菜品：白菜炒木耳、米饭、奥尔良炸鸡腿、小炒菜花')
console.log('- 杨志国总共有7种菜品，所以小计是7')
console.log('- 其他用户有1种菜品：测试菜品，所以小计是1')
console.log('- 小计表示的是该用户所有订单中菜品种类的总数量')

module.exports = { processTableData }

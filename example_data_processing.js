// 示例：根据 user_name 计算 foods_st 的 food_count 合计功能演示

const NP = require('number-precision')

// 模拟原始数据结构
const mockApiResponse = {
  code: 0,
  data: {
    results: [
      {
        "area_name": "A00空分现场",
        "l1_addr": "空分现场",
        "l2_addr": "A00空分现场",
        "l3_addr": null,
        "l4_addr": null,
        "l5_addr": null,
        "user_name": "刘宇",
        "phone": "15148386085",
        "remark": "",
        "orders_count": 1,
        "foods_st": [
          {
            "food_name": "尖椒炒茄子丝",
            "food_count": 1
          },
          {
            "food_name": "排骨炖豆角",
            "food_count": 1
          }
        ]
      },
      {
        "area_name": "A00空分现场",
        "l1_addr": "空分现场",
        "l2_addr": "A00空分现场",
        "l3_addr": null,
        "l4_addr": null,
        "l5_addr": null,
        "user_name": "吴仁奇",
        "phone": "13039562424",
        "remark": "",
        "orders_count": 1,
        "foods_st": [
          {
            "food_name": "米饭",
            "food_count": 1
          },
          {
            "food_name": "软炸鸡翅根",
            "food_count": 1
          }
        ]
      }
    ],
    other_info: [
      { order_payment_id: 'order_001' },
      { order_payment_id: 'order_002' }
    ]
  }
}

// 处理数据的函数（从 ShuReport.vue 中提取的逻辑）
function processTableData(res) {
  const tableData = []

  res.data.results.map((item, index) => {
    // 计算当前用户的食品数量小计
    let userSubtotal = 0
    item.foods_st.forEach(food => {
      userSubtotal = NP.plus(userSubtotal, food.food_count || 0)
    })

    item.foods_st.map(food => {
      tableData.push({
        id: res.data.other_info[index].order_payment_id,
        ...item,
        ...food,
        subtotal: userSubtotal, // 添加小计字段
        index
      })
    })
  })

  return tableData
}

// 演示处理结果
const processedData = processTableData(mockApiResponse)

console.log('处理后的表格数据：')
console.log(JSON.stringify(processedData, null, 2))

console.log('\n小计计算结果：')
processedData.forEach((row, index) => {
  console.log(`第${index + 1}行 - 用户: ${row.user_name}, 菜品: ${row.food_name}, 数量: ${row.food_count}, 小计: ${row.subtotal}`)
})

// 验证小计计算是否正确
console.log('\n验证小计计算：')
console.log('刘宇的小计应该是 2 (1+1):', processedData.find(row => row.user_name === '刘宇').subtotal)
console.log('吴仁奇的小计应该是 2 (1+1):', processedData.find(row => row.user_name === '吴仁奇').subtotal)

module.exports = { processTableData }

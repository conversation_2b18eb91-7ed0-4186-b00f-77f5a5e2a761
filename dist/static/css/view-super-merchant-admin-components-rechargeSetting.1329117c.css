.paysetting-wrapper{position:relative;margin-top:10px}.paysetting-wrapper .save-m-r{margin-right:10px}.paysetting-wrapper .tree-wrapper{width:auto;max-width:250px;background-color:#f8f9fa}.paysetting-wrapper .tree-wrapper .tree-search{margin-bottom:10px}.paysetting-wrapper .tree-wrapper .all-tree{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;line-height:30px;font-size:14px;cursor:pointer;padding-left:10px}.paysetting-wrapper .tree-wrapper .all-tree.is-current{position:relative;color:#fda04d;background-color:#fbeee6;font-weight:600}.paysetting-wrapper .tree-wrapper .all-tree .tree-search-icon{position:relative;display:inline-block;width:18px;height:40px;margin-right:5px}.paysetting-wrapper .tree-wrapper .all-tree .tree-search-icon img{display:inline-block;width:18px;height:18px;vertical-align:middle}.paysetting-wrapper .tree-wrapper .el-tree{background-color:#f8f9fa}.paysetting-wrapper .tree-wrapper .el-tree .el-tree-node__label{font-size:14px;color:#23282d}.paysetting-wrapper .tree-wrapper .el-tree .el-tree-node__content{height:30px}.paysetting-wrapper .tree-wrapper .tree-box .is-current>.el-tree-node__content{position:relative;background-color:#fbeee6!important}.paysetting-wrapper .paysetting-container{display:-webkit-box;display:-ms-flexbox;display:flex}.paysetting-wrapper .paysetting-container .paysetting-r{-webkit-box-flex:1;-ms-flex:1;flex:1;min-width:0;margin-left:10px}.paysetting-wrapper .paysetting-sub .wrapper-title{text-align:right;padding-bottom:20px;color:#fd9445}.paysetting-wrapper .paysetting-sub .sub-wrapper{margin-bottom:15px;border-radius:4px;border:1px solid #e0e6eb;background-color:#f8f9fa}.paysetting-wrapper .paysetting-sub .sub-wrapper .el-collapse{padding:0 20px;background-color:#f8f9fa}.paysetting-wrapper .paysetting-sub .sub-wrapper .el-collapse .el-collapse-item__header{background-color:#f8f9fa}.paysetting-wrapper .tips-r{position:absolute;right:45px}.paysetting-wrapper .is-active .tips-r .open,.paysetting-wrapper .tips-r .close{display:none}.paysetting-wrapper .is-active .tips-r .close{display:inline-block}.ps-paysetting-dialog .el-dialog__body{max-height:60vh;overflow-y:auto}.ps-paysetting-dialog .paysetting-dialog .el-form-item__label{line-height:1.2;text-align:justify}.ps-paysetting-dialog .paysetting-dialog .el-form-item__content{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;line-height:32px}.ps-paysetting-dialog .paysetting-dialog .el-form-item__content .el-input,.ps-paysetting-dialog .paysetting-dialog .el-form-item__content .vue-treeselect{max-width:324px}.ps-paysetting-dialog .paysetting-dialog .el-form-item__content .el-tooltip{margin-left:5px;cursor:pointer}.ps-paysetting-dialog .paysetting-dialog .vue-treeselect__control{height:32px}.ps-paysetting-dialog .paysetting-dialog .el-select{width:100%}